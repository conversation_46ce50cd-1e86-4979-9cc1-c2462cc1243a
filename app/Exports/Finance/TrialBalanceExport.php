<?php

namespace App\Exports\Finance;

use App\Enums\Finance\AccountCategory;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class TrialBalanceExport implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'trial-balance-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function collection()
    {
        $collection = collect();

        // Group accounts by category
        $groupedAccounts = collect($this->data['accounts'])->groupBy('category');

        // Define the order of categories for trial balance
        $categoryOrder = [
            // Assets
            AccountCategory::CashBank->value,
            AccountCategory::CurrentAsset->value,
            AccountCategory::AccountsReceivable->value,
            AccountCategory::Inventory->value,
            AccountCategory::NonCurrentAsset->value,
            AccountCategory::AccumulatedDepreciation->value,
            
            // Liabilities
            AccountCategory::CurrentLiability->value,
            AccountCategory::AccountsPayable->value,
            AccountCategory::NonCurrentLiability->value,
            
            // Equity
            AccountCategory::Equity->value,
            
            // Revenue
            AccountCategory::OperatingRevenue->value,
            AccountCategory::UncategorizedRevenue->value,
            
            // Expenses
            AccountCategory::CostOfGoodsSold->value,
            AccountCategory::CostOfSales->value,
            AccountCategory::OperatingExpense->value,
            AccountCategory::NonOperatingExpense->value,
            AccountCategory::UncategorizedExpense->value,
        ];

        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($categoryOrder as $categoryValue) {
            if (!$groupedAccounts->has($categoryValue)) {
                continue;
            }

            $category = AccountCategory::from($categoryValue);
            $accounts = $groupedAccounts->get($categoryValue);

            // Add category header
            $collection->push((object) [
                'type' => 'category',
                'code' => '',
                'name' => $category->getLabel(),
                'debit' => null,
                'credit' => null,
            ]);

            // Add accounts in this category
            foreach ($accounts as $account) {
                $collection->push((object) [
                    'type' => 'account',
                    'code' => $account['code'],
                    'name' => $account['name'],
                    'debit' => $account['debit'],
                    'credit' => $account['credit'],
                ]);

                $totalDebits += $account['debit'];
                $totalCredits += $account['credit'];
            }

            // Add empty row after each category
            $collection->push((object) [
                'type' => 'empty',
                'code' => '',
                'name' => '',
                'debit' => null,
                'credit' => null,
            ]);
        }

        // Add total row
        $collection->push((object) [
            'type' => 'total',
            'code' => '',
            'name' => 'TOTAL',
            'debit' => $totalDebits,
            'credit' => $totalCredits,
        ]);

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Debit (' . $this->data['currency_code'] . ')',
            'Credit (' . $this->data['currency_code'] . ')',
        ];
    }

    public function map($row): array
    {
        return [
            $row->code,
            $row->name,
            $row->debit !== null ? $row->debit * $this->data['exchange_rate'] : null,
            $row->credit !== null ? $row->credit * $this->data['exchange_rate'] : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }
}
