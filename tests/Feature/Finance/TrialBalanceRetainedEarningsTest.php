<?php

namespace Tests\Feature\Finance;

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntry;
use App\Models\Finance\JournalEntryItem;
use App\Traits\CalculatesEarnings;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase;

class TrialBalanceRetainedEarningsTest extends TestCase
{
    use CalculatesEarnings;
    use RefreshDatabase;

    protected CashAccount $revenueAccount;

    protected CashAccount $expenseAccount;

    public function createApplication()
    {
        return require __DIR__ . '/../../../bootstrap/app.php';
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Create test accounts
        $this->revenueAccount = CashAccount::factory()->create([
            'code' => '4000',
            'name' => 'Revenue Account',
            'category' => AccountCategory::OperatingRevenue,
        ]);

        $this->expenseAccount = CashAccount::factory()->create([
            'code' => '5000',
            'name' => 'Expense Account',
            'category' => AccountCategory::OperatingExpense,
        ]);
    }

    public function test_retained_earnings_calculated_by_year()
    {
        // Create journal entries for 2022
        $this->createJournalEntry('2022-06-01', $this->revenueAccount, 'c', 10000); // Revenue
        $this->createJournalEntry('2022-06-01', $this->expenseAccount, 'd', 6000);  // Expense
        // Net income 2022: 4000

        // Create journal entries for 2023
        $this->createJournalEntry('2023-06-01', $this->revenueAccount, 'c', 15000); // Revenue
        $this->createJournalEntry('2023-06-01', $this->expenseAccount, 'd', 8000);  // Expense
        // Net income 2023: 7000

        // Test retained earnings calculation for 2024 start date
        $startDate = Carbon::createFromDate(2024, 1, 1);
        $retainedEarnings = $this->calculateRetainedEarningsByYear($startDate);

        $this->assertCount(2, $retainedEarnings);

        // Check 2022 retained earnings
        $this->assertEquals(2022, $retainedEarnings[0]['year']);
        $this->assertEquals(4000, $retainedEarnings[0]['amount']);

        // Check 2023 retained earnings
        $this->assertEquals(2023, $retainedEarnings[1]['year']);
        $this->assertEquals(7000, $retainedEarnings[1]['amount']);
    }

    public function test_retained_earnings_excludes_current_year()
    {
        // Create journal entries for 2023
        $this->createJournalEntry('2023-06-01', $this->revenueAccount, 'c', 10000);
        $this->createJournalEntry('2023-06-01', $this->expenseAccount, 'd', 6000);

        // Create journal entries for 2024 (current year)
        $this->createJournalEntry('2024-06-01', $this->revenueAccount, 'c', 15000);
        $this->createJournalEntry('2024-06-01', $this->expenseAccount, 'd', 8000);

        // Test retained earnings calculation for 2024 start date
        $startDate = Carbon::createFromDate(2024, 1, 1);
        $retainedEarnings = $this->calculateRetainedEarningsByYear($startDate);

        // Should only include 2023, not 2024
        $this->assertCount(1, $retainedEarnings);
        $this->assertEquals(2023, $retainedEarnings[0]['year']);
        $this->assertEquals(4000, $retainedEarnings[0]['amount']);
    }

    public function test_retained_earnings_excludes_zero_amounts()
    {
        // Create journal entries for 2022 with zero net income
        $this->createJournalEntry('2022-06-01', $this->revenueAccount, 'c', 10000);
        $this->createJournalEntry('2022-06-01', $this->expenseAccount, 'd', 10000);
        // Net income 2022: 0

        // Create journal entries for 2023 with positive net income
        $this->createJournalEntry('2023-06-01', $this->revenueAccount, 'c', 15000);
        $this->createJournalEntry('2023-06-01', $this->expenseAccount, 'd', 8000);
        // Net income 2023: 7000

        $startDate = Carbon::createFromDate(2024, 1, 1);
        $retainedEarnings = $this->calculateRetainedEarningsByYear($startDate);

        // Should only include 2023 (non-zero), not 2022 (zero)
        $this->assertCount(1, $retainedEarnings);
        $this->assertEquals(2023, $retainedEarnings[0]['year']);
        $this->assertEquals(7000, $retainedEarnings[0]['amount']);
    }

    public function test_current_year_earnings_calculation()
    {
        // Create journal entries for current year (2024)
        $this->createJournalEntry('2024-03-01', $this->revenueAccount, 'c', 12000);
        $this->createJournalEntry('2024-03-01', $this->expenseAccount, 'd', 5000);
        // Net income YTD: 7000

        $currentYearStart = Carbon::createFromDate(2024, 1, 1);
        $endDate = Carbon::createFromDate(2024, 6, 30);

        $currentYearEarnings = $this->calculateEarnings($currentYearStart, $endDate);

        $this->assertEquals(7000, $currentYearEarnings['net_income']);
        $this->assertEquals(12000, $currentYearEarnings['total_revenue']);
        $this->assertEquals(5000, $currentYearEarnings['total_expense']);
    }

    private function createJournalEntry(string $date, CashAccount $account, string $type, float $amount): void
    {
        $entry = JournalEntry::create([
            'entry_date' => $date,
            'description' => 'Test entry',
            'reference' => 'TEST-' . uniqid(),
        ]);

        JournalEntryItem::create([
            'entry_id' => $entry->id,
            'account_id' => $account->id,
            'type' => $type,
            'amount' => $amount,
            'exchange_rate' => 1.0,
            'description' => 'Test item',
        ]);
    }
}
